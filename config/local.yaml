database:
  default: "root:ppapi@(data.meitengtech.com)/wukong?charset=utf8mb4&parseTime=True&loc=Local"
  max_idle_conns: 20
  max_open_conns: 20

database_cloud_sample:
  default: "root:ppapi@(data.meitengtech.com)/cloud_sample?charset=utf8mb4&parseTime=True&loc=Local"

cos:
  app_id: "1256345016"
  secret_id: "AKIDXxAGH7kBNK0bz84oKenjcT7mJekcptxH"
  secret_key: "BFF1a4KMBl8IhsqaAZkiTA2iMrOsEON8"
  region: "ap-shanghai"
  bucket: "keeper-saas-base-1256345016"
  domain: "https://keeper-saas-base-1256345016.cos.ap-shanghai.myqcloud.com"
  cdn: "https://keeper-saas-base-1256345016.cos.ap-shanghai.myqcloud.com"

oss:
  access-key: "LTAI5tEsuPJ4y5mETX8q9EmF"
  secret-key: "******************************"
  region: "cn-shenzhen"
  bucket: "meitengtech"
  domain: "https://meitengtech.oss-cn-shenzhen.aliyuncs.com"
  endpoint: "https://oss-cn-shenzhen.aliyuncs.com"
  role-session-name: "cloudApi"
  role-arn: "acs:ram::1502847884664317:role/cloudapi"
  object-dir-prefix: "drone"
  expire: 3600

redis:
  address: "data.meitengtech.com:6379"
  password: "ppapi"
  db: 0

mqtt:
  basic:
    broker: "svr.meitengtech.com"
    port: 1883
    client-id: "wukong-basic-client"
    user: JavaServer
    password: 123456
  drc:
    broker: "data.meitengtech.com"
    port: 1883
    client_id: "wukong-drc-client"
    user: ppapi
    password: ppapi

predict:
  #  address: "octopus-predict:8003"
  address: "127.0.0.1:8003"

helper:
  address: "boeing.meitengtech.com:5202"
#  address: "127.0.0.1:5202"

baidu_map:
  ak: "dyWSZfA0eqCcu4k0VTcmPPYMsGwp2x9K"
  uri: "https://api.map.baidu.com/geoconv/v1/"

live:
  ali:
    flv_url: "http://live.meitengtech.com/live/"
    pull_secret: "u4A2FuchRMsH3g39"
    rtmp_url: 'rtmp://push.meitengtech.com/live/'
    rtmp_push_secret: '40rwV73U86jwFwE6'
  srs:
    rtmp_url: "rtmp://**************:1935/live/"
    flv_url: "http://**************:8080/live/"
#  srs_dock:
#    rtmp_url: "rtmp://**************/live/"
#    rtmp_push_secret: "c46a48010f2e416ea04cd09edc9ca925"
#    flv_url: "http://**************/live/"

sms:
  ap_id: "xmdt010"
  secret_key: "Password@dt1"
  ec_name: "接口联调账号"
  sign: "bM16CfN2B"
  add_serial: "10657204006"
  url: "http://*************:1992/sms/norsubmit"

ai:
  api_key: "6f5100dd-0cc6-4b68-b2f1-b5083a94b957"

grpc:
  port: "5010"
