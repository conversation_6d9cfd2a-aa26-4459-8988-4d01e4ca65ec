package main

import (
	"context"
	"log"
	"time"
	"wukong-api/internal/pb"

	"google.golang.org/grpc"
)

func main() {
	// 连接到 gRPC 服务器
	conn, err := grpc.Dial("localhost:8080", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("连接失败: %v", err)
	}
	defer conn.Close()

	// 创建客户端
	client := pb.NewDeviceControlServiceClient(conn)

	// 示例1: 无人机控制
	log.Println("=== 无人机控制示例 ===")
	droneReq := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_DRONE,
		DockSn:     "DOCK001",
		DroneSn:    "DRONE001",
		DroneControl: &pb.DroneControl{
			X: 10.0, // 向前推进10米
			Y: 0.0,
			H: 0.0,
			W: 0.0,
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err := client.DeviceControl(ctx, droneReq)
	if err != nil {
		log.Fatalf("无人机控制失败: %v", err)
	}
	log.Printf("无人机控制结果: %v", resp.Succeed)

	// 示例2: 云台控制 - 拉近焦距 + 变焦云台
	log.Println("=== 云台控制示例1: 拉近焦距 ===")
	gimbalReq1 := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_GIMBAL,
		DockSn:     "DOCK001",
		DroneSn:    "DRONE001",
		GimbalControl: &pb.GimbalControl{
			PitchAdjust: 15.0,                           // 俯仰角向下15度
			YawAdjust:   90.0,                           // 偏航角右转90度
			ZoomType:    pb.ZoomType_ZOOM_TYPE_IN,       // 拉近焦距
			GimbalType:  pb.GimbalType_GIMBAL_TYPE_ZOOM, // 变焦云台
		},
	}

	ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err = client.DeviceControl(ctx, gimbalReq1)
	if err != nil {
		log.Fatalf("云台控制失败: %v", err)
	}
	log.Printf("云台控制结果: %v", resp.Succeed)

	// 示例3: 云台控制 - 拉远焦距 + 广角云台
	log.Println("=== 云台控制示例2: 拉远焦距 ===")
	gimbalReq2 := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_GIMBAL,
		DockSn:     "DOCK001",
		DroneSn:    "DRONE001",
		GimbalControl: &pb.GimbalControl{
			PitchAdjust: -10.0,                          // 俯仰角向上10度
			YawAdjust:   -45.0,                          // 偏航角左转45度
			ZoomType:    pb.ZoomType_ZOOM_TYPE_OUT,      // 拉远焦距
			GimbalType:  pb.GimbalType_GIMBAL_TYPE_WIDE, // 广角云台
		},
	}

	ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err = client.DeviceControl(ctx, gimbalReq2)
	if err != nil {
		log.Fatalf("云台控制失败: %v", err)
	}
	log.Printf("云台控制结果: %v", resp.Succeed)

	// 示例4: 拍照控制
	log.Println("=== 拍照控制示例 ===")
	photoReq := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_PHOTO,
		DockSn:     "DOCK001",
		DroneSn:    "DRONE001",
		// 拍照不需要额外参数
	}

	ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err = client.DeviceControl(ctx, photoReq)
	if err != nil {
		log.Fatalf("拍照控制失败: %v", err)
	}
	log.Printf("拍照控制结果: %v", resp.Succeed)

	log.Println("=== 所有操作完成 ===")
}

// 枚举值说明
/*
ActionType 枚举值：
- ACTION_TYPE_UNSPECIFIED = 0  // 未指定
- ACTION_TYPE_DRONE = 1        // 无人机控制
- ACTION_TYPE_GIMBAL = 2       // 云台控制
- ACTION_TYPE_PHOTO = 3        // 拍照

ZoomType 枚举值：
- ZOOM_TYPE_UNSPECIFIED = 0    // 未指定
- ZOOM_TYPE_IN = 1             // 拉近焦距
- ZOOM_TYPE_OUT = 2            // 拉远焦距

GimbalType 枚举值：
- GIMBAL_TYPE_UNSPECIFIED = 0  // 未指定
- GIMBAL_TYPE_ZOOM = 1         // 变焦
- GIMBAL_TYPE_WIDE = 2         // 广角
*/
