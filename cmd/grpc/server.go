package grpc

import (
	"log"
	"net"
	"wukong-api/internal/config"
	"wukong-api/internal/rpc"

	"google.golang.org/grpc"
)

// StartGRPC 启动gRPC服务器
func StartGRPC() {
	cfg := config.GetConfig()

	// 监听指定端口
	lis, err := net.Listen("tcp", ":"+cfg.GRPC.Port)
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	// 创建gRPC服务器
	s := grpc.NewServer()

	// 注册所有RPC服务
	registry := rpc.NewRegistry()
	registry.RegisterServices(s)

	log.Printf("gRPC server listening on :%s", cfg.GRPC.Port)
	if err := s.Serve(lis); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
