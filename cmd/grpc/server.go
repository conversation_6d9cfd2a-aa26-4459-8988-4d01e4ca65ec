package grpc

// StartGRPC todo v2
func StartGRPC() {
	//cfg := config.GetConfig()
	//
	//lis, err := net.Listen("tcp", ":"+cfg.GRPC.Algorithm.Port)
	//if err != nil {
	//	log.Fatalf("failed to listen: %v", err)
	//}
	//
	//s := grpc.NewServer()
	//
	//// 注册所有RPC服务
	//registry := rpc.NewRegistry()
	//registry.RegisterServices(s)
	//
	//log.Printf("gRPC server listening on :%s", cfg.GRPC.Algorithm.Port)
	//if err := s.Serve(lis); err != nil {
	//	log.Fatalf("failed to serve: %v", err)
	//}
}
