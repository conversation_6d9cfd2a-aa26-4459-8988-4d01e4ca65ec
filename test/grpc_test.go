package test

import (
	"context"
	"testing"
	"time"
	"wukong-api/internal/pb"
	"wukong-api/internal/rpc"

	"net"

	"google.golang.org/grpc"
	"google.golang.org/grpc/test/bufconn"
)

const bufSize = 1024 * 1024

var lis *bufconn.Listener

func init() {
	lis = bufconn.Listen(bufSize)
	s := grpc.NewServer()

	// 注册服务
	registry := rpc.NewRegistry()
	registry.RegisterServices(s)

	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
}

func bufDialer(context.Context, string) (net.Conn, error) {
	return lis.Dial()
}

func TestDeviceControl(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试无人机控制
	req := &pb.DeviceControlRequest{
		ActionType: "1",
		DockSn:     "test-dock-001",
		DroneSn:    "test-drone-001",
		DroneControl: &pb.DroneControl{
			X: 10.5,
			Y: 20.3,
			H: 50.0,
			W: 5.0,
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("DeviceControl response: succeed=%v", resp.Succeed)
}

func TestGimbalControl(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试云台控制
	req := &pb.DeviceControlRequest{
		ActionType: "2",
		DockSn:     "test-dock-001",
		DroneSn:    "test-drone-001",
		GimbalControl: &pb.GimbalControl{
			PitchAdjust: 15.0,
			YawAdjust:   90.0,
			ZoomType:    1,
			GimbalType:  2,
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("GimbalControl response: succeed=%v", resp.Succeed)
}

func TestPhotoCapture(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试拍照控制
	req := &pb.DeviceControlRequest{
		ActionType: "3",
		DockSn:     "test-dock-001",
		DroneSn:    "test-drone-001",
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("PhotoCapture response: succeed=%v", resp.Succeed)
}
