package test

import (
	"context"
	"testing"
	"time"
	"wukong-api/internal/pb"
	"wukong-api/internal/rpc"

	"net"

	"google.golang.org/grpc"
	"google.golang.org/grpc/test/bufconn"
)

const bufSize = 1024 * 1024

var lis *bufconn.Listener

func init() {
	lis = bufconn.Listen(bufSize)
	s := grpc.NewServer()

	// 注册服务
	registry := rpc.NewRegistry()
	registry.RegisterServices(s)

	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
}

func bufDialer(context.Context, string) (net.Conn, error) {
	return lis.Dial()
}

func TestDeviceControlWithEnums(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试无人机控制（使用枚举）
	req := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_DRONE,
		DockSn:     "test-dock-001",
		DroneSn:    "test-drone-001",
		DroneControl: &pb.DroneControl{
			X: 10.5,
			Y: 20.3,
			H: 50.0,
			W: 5.0,
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("DroneControl response: succeed=%v", resp.Succeed)
}

func TestGimbalControlWithEnums(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试云台控制（使用枚举）
	req := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_GIMBAL,
		DockSn:     "test-dock-001",
		DroneSn:    "test-drone-001",
		GimbalControl: &pb.GimbalControl{
			PitchAdjust: 15.0,
			YawAdjust:   90.0,
			ZoomType:    pb.ZoomType_ZOOM_TYPE_IN,       // 拉近焦距
			GimbalType:  pb.GimbalType_GIMBAL_TYPE_ZOOM, // 变焦
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("GimbalControl response: succeed=%v", resp.Succeed)
}

func TestPhotoCaptureWithEnum(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试拍照控制（使用枚举）
	req := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_PHOTO,
		DockSn:     "test-dock-001",
		DroneSn:    "test-drone-001",
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("PhotoCapture response: succeed=%v", resp.Succeed)
}

func TestGimbalControlZoomOut(t *testing.T) {
	ctx := context.Background()
	conn, err := grpc.DialContext(ctx, "bufnet", grpc.WithContextDialer(bufDialer), grpc.WithInsecure())
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}
	defer conn.Close()

	client := pb.NewDeviceControlServiceClient(conn)

	// 测试云台控制 - 拉远焦距 + 广角
	req := &pb.DeviceControlRequest{
		ActionType: pb.ActionType_ACTION_TYPE_GIMBAL,
		DockSn:     "test-dock-002",
		DroneSn:    "test-drone-002",
		GimbalControl: &pb.GimbalControl{
			PitchAdjust: -10.0,
			YawAdjust:   -45.0,
			ZoomType:    pb.ZoomType_ZOOM_TYPE_OUT,      // 拉远焦距
			GimbalType:  pb.GimbalType_GIMBAL_TYPE_WIDE, // 广角
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := client.DeviceControl(ctx, req)
	if err != nil {
		t.Fatalf("DeviceControl failed: %v", err)
	}

	if !resp.Succeed {
		t.Errorf("Expected succeed=true, got succeed=%v", resp.Succeed)
	}

	t.Logf("GimbalControl ZoomOut response: succeed=%v", resp.Succeed)
}

func TestEnumValues(t *testing.T) {
	// 测试枚举值
	t.Logf("ActionType values:")
	t.Logf("  ACTION_TYPE_DRONE: %d", pb.ActionType_ACTION_TYPE_DRONE)
	t.Logf("  ACTION_TYPE_GIMBAL: %d", pb.ActionType_ACTION_TYPE_GIMBAL)
	t.Logf("  ACTION_TYPE_PHOTO: %d", pb.ActionType_ACTION_TYPE_PHOTO)

	t.Logf("ZoomType values:")
	t.Logf("  ZOOM_TYPE_IN: %d", pb.ZoomType_ZOOM_TYPE_IN)
	t.Logf("  ZOOM_TYPE_OUT: %d", pb.ZoomType_ZOOM_TYPE_OUT)

	t.Logf("GimbalType values:")
	t.Logf("  GIMBAL_TYPE_ZOOM: %d", pb.GimbalType_GIMBAL_TYPE_ZOOM)
	t.Logf("  GIMBAL_TYPE_WIDE: %d", pb.GimbalType_GIMBAL_TYPE_WIDE)
}
