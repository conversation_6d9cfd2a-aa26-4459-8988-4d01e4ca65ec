package rpc

import (
	"context"
	"wukong-api/internal/pb"
	"wukong-api/internal/repo"

	"go.uber.org/zap"
)

// DeviceControlServer 设备控制gRPC服务器
type DeviceControlServer struct {
	pb.UnimplementedDeviceControlServiceServer
	logger *zap.SugaredLogger
}

// NewDeviceControlServer 创建新的设备控制服务器实例
func NewDeviceControlServer() *DeviceControlServer {
	return &DeviceControlServer{
		logger: repo.GetLogger().Named("DeviceControlRPC"),
	}
}

// DeviceControl 处理设备控制请求
func (s *DeviceControlServer) DeviceControl(ctx context.Context, req *pb.DeviceControlRequest) (*pb.DeviceControlResponse, error) {
	s.logger.Infof("收到设备控制请求 - ActionType: %s, DockSN: %s, DroneSN: %s",
		req.ActionType.String(), req.DockSn, req.DroneSn)

	// 根据action_type处理不同类型的控制请求
	switch req.ActionType {
	case pb.ActionType_ACTION_TYPE_DRONE:
		// 无人机控制
		if req.DroneControl != nil {
			s.logger.Infof("无人机控制 - X: %f, Y: %f, H: %f, W: %f",
				req.DroneControl.X, req.DroneControl.Y, req.DroneControl.H, req.DroneControl.W)
			// TODO: 实现无人机控制逻辑
			// 例如：无人机朝机头方向推进、机头右转90度等
		}
	case pb.ActionType_ACTION_TYPE_GIMBAL:
		// 云台控制
		if req.GimbalControl != nil {
			s.logger.Infof("云台控制 - PitchAdjust: %f, YawAdjust: %f, ZoomType: %s, GimbalType: %s",
				req.GimbalControl.PitchAdjust, req.GimbalControl.YawAdjust,
				req.GimbalControl.ZoomType.String(), req.GimbalControl.GimbalType.String())

			// 处理变焦类型
			switch req.GimbalControl.ZoomType {
			case pb.ZoomType_ZOOM_TYPE_IN:
				s.logger.Info("执行拉近焦距操作")
			case pb.ZoomType_ZOOM_TYPE_OUT:
				s.logger.Info("执行拉远焦距操作")
			}

			// 处理云台类型
			switch req.GimbalControl.GimbalType {
			case pb.GimbalType_GIMBAL_TYPE_ZOOM:
				s.logger.Info("使用变焦云台")
			case pb.GimbalType_GIMBAL_TYPE_WIDE:
				s.logger.Info("使用广角云台")
			}

			// TODO: 实现云台控制逻辑
			// 例如：云台右转90度等
		}
	case pb.ActionType_ACTION_TYPE_PHOTO:
		// 拍照控制
		s.logger.Info("执行拍照操作")
		// TODO: 实现拍照逻辑
	default:
		s.logger.Warnf("未知的action_type: %s", req.ActionType.String())
		return &pb.DeviceControlResponse{
			Succeed: false,
		}, nil
	}

	// TODO: 这里应该调用实际的设备控制接口
	// 目前先返回成功响应
	return &pb.DeviceControlResponse{
		Succeed: true,
	}, nil
}

/*
设备控制类型说明：

ActionType 枚举：
- ACTION_TYPE_DRONE (1): 无人机控制
  - 无人机朝机头方向推进 p 米。p为无人机两三个身位
  - 机头右转90
  - 无人机沿着机头方向推进 n 米。推到桥底中间
  - 无人机沿着机头方向推进一点

- ACTION_TYPE_GIMBAL (2): 云台控制
  - 云台右转90
  - 俯仰角调整
  - 偏航角调整

- ACTION_TYPE_PHOTO (3): 拍照

ZoomType 枚举：
- ZOOM_TYPE_IN (1): 拉近焦距
- ZOOM_TYPE_OUT (2): 拉远焦距

GimbalType 枚举：
- GIMBAL_TYPE_ZOOM (1): 变焦
- GIMBAL_TYPE_WIDE (2): 广角
*/
