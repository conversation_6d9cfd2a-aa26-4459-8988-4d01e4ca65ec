package rpc

import (
	"context"
	"wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/internal/dto"
	"wukong-api/internal/fileds"
	"wukong-api/internal/pb"
	"wukong-api/internal/repo"
	"wukong-api/internal/service"

	"go.uber.org/zap"
)

// DeviceControlServer 设备控制gRPC服务器
type DeviceControlServer struct {
	pb.UnimplementedDeviceControlServiceServer
	logger *zap.SugaredLogger
}

// NewDeviceControlServer 创建新的设备控制服务器实例
func NewDeviceControlServer() *DeviceControlServer {
	return &DeviceControlServer{
		logger: repo.GetLogger().Named("DeviceControlRPC"),
	}
}

// DeviceControl 处理设备控制请求
func (s *DeviceControlServer) DeviceControl(ctx context.Context, req *pb.DeviceControlRequest) (*pb.DeviceControlResponse, error) {
	s.logger.Infof("收到设备控制请求 - ActionType: %s, DockSN: %s, DroneSN: %s",
		req.ActionType.String(), req.DockSn, req.DroneSn)

	// 根据action_type处理不同类型的控制请求
	switch req.ActionType {
	case pb.ActionType_ACTION_TYPE_DRONE:
		// 无人机控制
		if req.DroneControl != nil {
			s.logger.Infof("无人机控制 - X: %f, Y: %f, H: %f, W: %f",
				req.DroneControl.X, req.DroneControl.Y, req.DroneControl.H, req.DroneControl.W)
			// TODO: 实现无人机控制逻辑
			// 例如：无人机朝机头方向推进、机头右转90度等
		}
	case pb.ActionType_ACTION_TYPE_GIMBAL:
		// 云台控制
		if req.GimbalControl != nil {
			s.logger.Infof("云台控制 - PitchAdjust: %f, YawAdjust: %f, ZoomType: %s, GimbalType: %s",
				req.GimbalControl.PitchAdjust, req.GimbalControl.YawAdjust,
				req.GimbalControl.ZoomType.String(), req.GimbalControl.GimbalType.String())

			// 实现云台控制逻辑
			err := s.handleGimbalControl(req.DockSn, req.GimbalControl)
			if err != nil {
				s.logger.Errorf("云台控制失败: %v", err)
				return &pb.DeviceControlResponse{
					Succeed: false,
				}, nil
			}
		}
	case pb.ActionType_ACTION_TYPE_PHOTO:
		// 拍照控制
		s.logger.Info("执行拍照操作")
		err := s.handlePhotoCapture(req.DockSn)
		if err != nil {
			s.logger.Errorf("拍照失败: %v", err)
			return &pb.DeviceControlResponse{
				Succeed: false,
			}, nil
		}
	default:
		s.logger.Warnf("未知的action_type: %s", req.ActionType.String())
		return &pb.DeviceControlResponse{
			Succeed: false,
		}, nil
	}

	// TODO: 这里应该调用实际的设备控制接口
	// 目前先返回成功响应
	return &pb.DeviceControlResponse{
		Succeed: true,
	}, nil
}

/*
设备控制类型说明：

ActionType 枚举：
- ACTION_TYPE_DRONE (1): 无人机控制
  - 无人机朝机头方向推进 p 米。p为无人机两三个身位
  - 机头右转90
  - 无人机沿着机头方向推进 n 米。推到桥底中间
  - 无人机沿着机头方向推进一点

- ACTION_TYPE_GIMBAL (2): 云台控制
  - 云台右转90
  - 俯仰角调整
  - 偏航角调整

- ACTION_TYPE_PHOTO (3): 拍照

ZoomType 枚举：
- ZOOM_TYPE_IN (1): 拉近焦距
- ZOOM_TYPE_OUT (2): 拉远焦距

GimbalType 枚举：
- GIMBAL_TYPE_ZOOM (1): 变焦
- GIMBAL_TYPE_WIDE (2): 广角
*/

// handleGimbalControl 处理云台控制
func (s *DeviceControlServer) handleGimbalControl(dockSn string, gimbalControl *pb.GimbalControl) error {
	dockControlSvc := service.NewDockControlService()

	// 处理俯仰角和偏航角调整
	if gimbalControl.PitchAdjust != 0 || gimbalControl.YawAdjust != 0 {
		s.logger.Infof("执行云台角度调整 - Pitch: %f, Yaw: %f",
			gimbalControl.PitchAdjust, gimbalControl.YawAdjust)

		// 使用相机拖拽控制来调整云台角度
		payloadCmd := dto.PayloadCommandsReq{
			DockSn: dockSn,
			Method: fileds.Camera_Screen_Drag,
			Data: request.DronePayloadRequest{
				PayloadIndex: "99-0-0", // 默认负载索引
				PitchSpeed:   &gimbalControl.PitchAdjust,
				YawSpeed:     &gimbalControl.YawAdjust,
			},
		}

		_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
		if err != nil {
			s.logger.Errorf("云台角度调整失败: %v, errMsg: %s", err, errMsg)
			return err
		}
		s.logger.Info("云台角度调整成功")
	}

	// 处理变焦控制
	if gimbalControl.ZoomType != pb.ZoomType_ZOOM_TYPE_UNSPECIFIED {
		var zoomFactor float64
		switch gimbalControl.ZoomType {
		case pb.ZoomType_ZOOM_TYPE_IN:
			s.logger.Info("执行拉近焦距操作")
			zoomFactor = 2.0 // 拉近焦距，增加变焦倍数
		case pb.ZoomType_ZOOM_TYPE_OUT:
			s.logger.Info("执行拉远焦距操作")
			zoomFactor = 0.5 // 拉远焦距，减少变焦倍数
		}

		payloadCmd := dto.PayloadCommandsReq{
			DockSn: dockSn,
			Method: fileds.CAMERA_FOCAL_LENGTH_SET,
			Data: request.DronePayloadRequest{
				PayloadIndex: "99-0-0",
				ZoomFactor:   &zoomFactor,
			},
		}

		_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
		if err != nil {
			s.logger.Errorf("变焦控制失败: %v, errMsg: %s", err, errMsg)
			return err
		}
		s.logger.Infof("变焦控制成功，变焦倍数: %f", zoomFactor)
	}

	// 处理云台类型（这里主要是记录日志，实际的云台类型切换可能需要其他API）
	switch gimbalControl.GimbalType {
	case pb.GimbalType_GIMBAL_TYPE_ZOOM:
		s.logger.Info("使用变焦云台模式")
	case pb.GimbalType_GIMBAL_TYPE_WIDE:
		s.logger.Info("使用广角云台模式")
	}

	return nil
}

// handlePhotoCapture 处理拍照控制
func (s *DeviceControlServer) handlePhotoCapture(dockSn string) error {
	dockControlSvc := service.NewDockControlService()

	// 执行拍照命令
	payloadCmd := dto.PayloadCommandsReq{
		DockSn: dockSn,
		Method: fileds.CAMERA_PHOTO_TAKE,
		Data: request.DronePayloadRequest{
			PayloadIndex: "99-0-0", // 默认负载索引
		},
	}

	_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
	if err != nil {
		s.logger.Errorf("拍照失败: %v, errMsg: %s", err, errMsg)
		return err
	}

	s.logger.Info("拍照成功")
	return nil
}
