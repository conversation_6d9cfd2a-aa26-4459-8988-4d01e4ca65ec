package rpc

// todo v2 设备控制rpc接口

//type Server struct {
//	pb.UnimplementedAlgorithmServiceServer
//	logger *zap.SugaredLogger
//}
//
//func NewServer() *Server {
//	return &Server{
//		logger: repo.GetLogger().Named("AlgorithmRPC"),
//	}
//}
//
//func (s *Server) ProcessData(ctx context.Context, req *pb.ProcessDataRequest) (*pb.ProcessDataResponse, error) {
//	s.logger.Infof("Processing algorithm data: %s", req.AlgorithmId)
//
//	// 实现算法处理逻辑
//
//	return &pb.ProcessDataResponse{
//		Success: true,
//		Result:  "处理成功",
//	}, nil
//}
//
//func (s *Server) HealthCheck(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
//	return &pb.HealthCheckResponse{
//		Healthy: true,
//		Version: "1.0.0",
//	}, nil
//}

//

//action_type:1 无人机朝机头方向推进 p 米。p为无人机两三个身位
//action_type:1 机头右转90
//action_type:1 无人机沿着机头方向推进 n 米。推到桥底中间
//action_type:1 无人机沿着机头方向推进一点
//
//action_type:2 云台调整
//云台右转90

// action_type:3 拍照
