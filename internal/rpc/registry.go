package rpc

import (
	"wukong-api/internal/pb"

	"google.golang.org/grpc"
)

// Registry gRPC服务注册器
type Registry struct {
	DeviceControlServer *DeviceControlServer
}

// NewRegistry 创建新的服务注册器
func NewRegistry() *Registry {
	return &Registry{
		DeviceControlServer: NewDeviceControlServer(),
	}
}

// RegisterServices 注册所有gRPC服务
func (r *Registry) RegisterServices(s *grpc.Server) {
	// 注册设备控制服务
	pb.RegisterDeviceControlServiceServer(s, r.DeviceControlServer)
}
