package service

import (
	"encoding/json"
	"fmt"
	"math"
	"time"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	live_stream_cloud_enum "wukong-api/cloud_sdk/cloud_api/live_stream/cloud_enum"

	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/fileds"
	"wukong-api/internal/repo"
)

// handleRealTimeControl 处理实时控制决策
func (s *JobDataProcessorService) handleRealTimeControlResp(isSucceeded bool, data dto.RealTimeControlResp) {
	connID := fmt.Sprintf("%s-%s", fileds.ServerRealTimeControl, data.DockSn)
	lockKey := fmt.Sprintf(fileds.RealTimeControlLock, connID)

	jobID := data.JobID

	if !isSucceeded {
		// 释放锁
		if err := s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("handleRealTimeControlResp: del lock key: %s, err: %v", lockKey, err)
			return
		}

		s.logger.Warnf("handleRealTimeControlResp: predict server handle data failed, del lock key: %s", lockKey)
		return
	}

	wsManager := repo.NewWsManager()
	// 检查websocket连接
	if _, ok := wsManager.WsConn[connID]; !ok {
		s.logger.Errorf("handleRealTimeControlResp: websocket connection not found, connID: %s", connID)
		return
	}

	dockControlSvc := NewDockControlService()
	drcRedisSvc := NewDockDRCRedisService()
	waylineRedisSvc := NewWaylineRedisService()
	liveStreamSvc := NewLiveStreamService()
	liveStreamRedisSvc := liveStreamRedisService

	// 决策数据分两类
	switch data.ActionType {
	case enum.AIControlType_None:
		s.logger.Infof("handleRealTimeControlResp: no control action, jobID: %s", jobID)

		// 释放控制权锁
		aiControlLockKey := fmt.Sprintf(fileds.AI_Control_Lock, data.DockSn)
		if err := s.redis.Del(aiControlLockKey).Err(); err != nil {
			s.logger.Errorf("handleRealTimeControlResp: del ai control lock failed, lockKey: %s, err: %v", aiControlLockKey, err)
			return
		}

	case enum.AIControlType_DroneControl:
		// todo 飞控控制
		// 1.执行决策
		// 2.准备数据，拍照更新深度信息
		// 3.ws通知算法决策 - 发送下一轮控制请求

	case enum.AIControlType_GimbalControl:
		// 云台调整
		if data.AIGimbalControl == nil {
			s.logger.Errorf("handleRealTimeControlResp: ai_gimbal_control data is nil, jobID: %s", jobID)
			return
		}

		// 1.执行决策
		// 云台模式切换
		if data.AIGimbalControl.GimbalType == 1 || data.AIGimbalControl.GimbalType == 2 {
			var cameraType live_stream_cloud_enum.LensChangeVideoTypeEnum
			if data.AIGimbalControl.GimbalType == 1 {
				cameraType = live_stream_cloud_enum.LensChangeVideo_ZOOM
			} else {
				cameraType = live_stream_cloud_enum.LensChangeVideo_WIDE
			}

			// 检查是否已为该模式
			isIgnore := false

			liveStreaming, _ := liveStreamRedisSvc.getLiveStreaming(data.DroneSn)
			if liveStreaming != nil && liveStreaming.LiveStatus != nil {
				for _, status := range liveStreaming.LiveStatus {
					if IsGimbalByPayloadModelKey(status.CameraIndex) && status.VideoType.Value() == cameraType.Value() {
						isIgnore = true
						break
					}
				}
			}

			if !isIgnore {
				err, errMsg := liveStreamSvc.LiveLensChange(data.DockSn, "99-0-0", cameraType)
				if err != nil {
					s.logger.Errorf("flightTaskTransfer, LiveLensChange failed, err: %v, errMsg: %s", err, errMsg)
					//return
				} else {
					s.logger.Infof("flightTaskTransfer, do LiveLensChange success, jobId: %s, cameraType: %s", jobID, cameraType.Value())
				}

				time.Sleep(300 * time.Millisecond)
			}
		}
		// 云台角度调整
		if data.AIGimbalControl.YawAdjust != 0 || data.AIGimbalControl.PitchAdjust != 0 {
			// pitch和yaw每次只能调整15度，传入的参数需要计算doNumber，然后把每次的要调的度数算好，累计能达到调整读数的要求
			pitchAdjust := data.AIGimbalControl.PitchAdjust
			yawAdjust := data.AIGimbalControl.YawAdjust

			// 计算需要调整的次数，每次最多15度
			maxAdjustPerStep := 15.0
			pitchSteps := int(math.Abs(pitchAdjust)/maxAdjustPerStep) + 1
			yawSteps := int(math.Abs(yawAdjust)/maxAdjustPerStep) + 1
			totalSteps := pitchSteps
			if yawSteps > pitchSteps {
				totalSteps = yawSteps
			}

			// 计算每步的调整量
			pitchSpeedPerStep := pitchAdjust / float64(totalSteps)
			yawSpeedPerStep := yawAdjust / float64(totalSteps)

			for i := 0; i < totalSteps; i++ {
				payloadCmd := dto.PayloadCommandsReq{
					DockSn: data.DockSn,
					Method: fileds.Camera_Screen_Drag,
					Data: device_request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						Locked:       &[]bool{false}[0],
						PitchSpeed:   &pitchSpeedPerStep,
						YawSpeed:     &yawSpeedPerStep,
					},
				}

				_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
				if err != nil {
					s.logger.Errorf("handleRealTimeControlResp: camera_screen_drag failed, do_time: %d, pitch_speed: %v, yaw_speed: %v, jobID: %s, err: %v, errMsg: %s", i, pitchSpeedPerStep, yawSpeedPerStep, jobID, err, errMsg)
					continue
				} else {
					s.logger.Infof("handleRealTimeControlResp: camera_screen_drag success, do_time: %d, pitch_speed: %v, yaw_speed: %v, jobID: %s", i, pitchSpeedPerStep, yawSpeedPerStep, jobID)
				}

				time.Sleep(300 * time.Millisecond)
			}
		}
		// 焦距调整
		if data.AIGimbalControl.ZoomType == 1 || data.AIGimbalControl.ZoomType == 2 {
			// 获取当前变焦倍数
			cameraOsd, err := drcRedisSvc.getDrcCamera(data.DockSn)
			if err != nil {
				s.logger.Errorf("handleRealTimeControlResp: get camera osd failed, jobID: %s, err: %v", jobID, err)
				//return
			} else if cameraOsd == nil {
				s.logger.Errorf("handleRealTimeControlResp: camera osd is nil, jobID: %s", jobID)
				//return
			}

			var currentZoomFactor float64
			if cameraOsd != nil {
				currentZoomFactor = cameraOsd.ZoomLense.ZoomFactor
			}

			setZoomFactor := currentZoomFactor

			if data.AIGimbalControl.ZoomType == 1 {
				// 放大
				setZoomFactor += 1
			} else {
				// 缩小
				setZoomFactor -= 1
			}

			payloadCmd := dto.PayloadCommandsReq{
				DockSn: data.DockSn,
				Method: fileds.CAMERA_FOCAL_LENGTH_SET,
				Data: device_request.DronePayloadRequest{
					PayloadIndex: "99-0-0",
					CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
					ZoomFactor:   &setZoomFactor,
				},
			}

			_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
			if err != nil {
				s.logger.Errorf("handleRealTimeControlResp: zoom failed, jobID: %s, err: %v, errMsg: %s", jobID, err, errMsg)
				//return
			} else {
				s.logger.Infof("handleRealTimeControlResp: zoom success, jobID: %s, setZoomFactor: %v", jobID, setZoomFactor)
			}
			time.Sleep(300 * time.Millisecond)
		}

		// 2.准备数据：深度信息、相机焦距mm、gimbal_pitch、gimbal_yaw
		// fixme 先写死，后续再优化，需要从实际设备状态获取
		var realTimeControlReq dto.RealTimeControlReq

		waylineJob, err := waylineRedisSvc.GetRunningInFlightWaylineJob(data.DockSn)
		if err != nil {
			s.logger.Warnf("handleRealTimeControlResp, get running wayline job failed, gatewaySn: %s, err: %v", data.DockSn, err)
			//return
		}

		if waylineJob == nil || waylineJob.WaylineManageId == 0 {
			s.logger.Warnf("handleRealTimeControlResp, wayline job is nil, dockSn: %s", data.DockSn)
			//return

			realTimeControlReq = dto.RealTimeControlReq{
				InitialDistance: 17.551, // 初始相机与目标之间的距离（米）
				FocalLength:     19.4,   // 相机焦距（毫米）
				InitialPitch:    0.0,    // 初始云台俯仰角（度）
				InitialYaw:      0.0,    // 初始云台偏航角（度）
			}

			s.logger.Infof("handleRealTimeControlResp: use default real time control req, dockSn: %s", data.DockSn)
		} else {
			var ok bool
			realTimeControlReq, ok = fileds.RealTimeControlReqData[waylineJob.WaylineManageId]
			if !ok {
				s.logger.Warnf("handleRealTimeControlResp: real time control req not found, WaylineManageId: %d", waylineJob.WaylineManageId)
				return
			}

			s.logger.Infof("handleRealTimeControlResp: use real time control req from wayline job, dockSn: %s, WaylineManageId: %d", data.DockSn, waylineJob.WaylineManageId)
		}

		// 3.ws通知算法决策 - 发送下一轮控制请求
		req := dto.JobDataProcessingReq{
			Type: fileds.RealTimeControlReq,
			Data: realTimeControlReq,
		}

		bytes, err := json.Marshal(&req)
		if err != nil {
			s.logger.Errorf("handleRealTimeControlResp: failed to encode data, err: %v", err)
			return
		}

		wsManager.SendData(bytes, connID)
		s.logger.Infof("handleRealTimeControlResp: sent next control request for continuous control, jobID: %s", jobID)

	case enum.AIControlType_TakePhoto:
		// 拍照
		payloadCmd := dto.PayloadCommandsReq{
			DockSn: data.DockSn,
			Method: fileds.CAMERA_PHOTO_TAKE,
			Data: device_request.DronePayloadRequest{
				PayloadIndex: "99-0-0",
			},
		}

		_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
		if err != nil {
			s.logger.Errorf("handleRealTimeControlResp: take photo failed, jobID: %s, err: %v, errMsg: %s", jobID, err, errMsg)
			return
		}

		s.logger.Infof("handleRealTimeControlResp: take photo success, jobID: %s", jobID)

		// 释放控制权锁
		aiControlLockKey := fmt.Sprintf(fileds.AI_Control_Lock, data.DockSn)
		if err = s.redis.Del(aiControlLockKey).Err(); err != nil {
			s.logger.Errorf("handleRealTimeControlResp: del ai control lock failed, lockKey: %s, err: %v", aiControlLockKey, err)
			return
		}

	}
}
