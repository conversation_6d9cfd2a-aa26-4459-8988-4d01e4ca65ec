package service

import (
	"fmt"
	"strings"
	"sync"
	"time"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	live_stream_cloud_enum "wukong-api/cloud_sdk/cloud_api/live_stream/cloud_enum"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/fileds"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"
	"wukong-api/pkg/utils"

	"github.com/goccy/go-json"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	inspectionStrategyServiceOnce sync.Once
	inspectionStrategyService     *InspectionStrategyService
)

type InspectionStrategyService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
	redis  *redis.Client
}

func NewInspectionStrategyService() *InspectionStrategyService {
	inspectionStrategyServiceOnce.Do(func() {
		inspectionStrategyService = &InspectionStrategyService{
			db:     repo.GetDatabase(),
			logger: repo.GetLogger(),
			redis:  repo.GetRedis(0),
		}
	})
	return inspectionStrategyService
}

func InspectionStrategyWorker() {
	go func() {
		inspectionStrategySvc := NewInspectionStrategyService()
		redisClient := repo.GetRedis(0)
		logger := repo.GetLogger().Named("InspectionStrategyWorker")

		ticker := time.NewTicker(500 * time.Millisecond)

		for {
			// 遍历巡检状态
			pattern := fmt.Sprintf(fileds.InspectionStatus, "*")
			keyList, _ := redisClient.Keys(pattern).Result()

			for _, key := range keyList {
				split := strings.Split(key, fileds.InspectionStatus)
				gatewaySn := split[1]

				// 检查锁，有锁跳过，没锁则上锁并执行
				executeDecisionLockKey := fmt.Sprintf(fileds.ExecuteDecisionLock, gatewaySn)

				acquired, err := redisClient.SetNX(executeDecisionLockKey, "locked", fileds.ExecuteDecisionLockTimeout).Result()
				if err != nil {
					logger.Errorf("failed to acquire lock: %v", err)
					continue
				}
				if !acquired {
					continue
				}

				go func() {
					var isReleaseLock bool
					isReleaseLock, err = inspectionStrategySvc.handleInspectionStrategy(gatewaySn)
					if err != nil {
						logger.Errorf("handleInspectionStrategy failed, gatewaySn: %s, err: %v", gatewaySn, err)
					}

					// 函数返回是否释放锁
					if isReleaseLock {
						redisClient.Del(executeDecisionLockKey)
					}
				}()
			}

			<-ticker.C
		}
	}()
}

// handleInspectionStrategy 轮询巡检状态，执行巡检策略
func (s *InspectionStrategyService) handleInspectionStrategy(gatewaySn string) (isReleaseLock bool, err error) {
	statusSvc := NewInspectionStatusService()
	//jobDataProcessorSvc := NewJobDataProcessorService()
	deviceRedisSvc := NewDeviceRedisService()
	inspectionStatusSvc := NewInspectionStatusService()
	waylineManageSvc := NewWaylineManageService()
	waylineFileSvc := NewWaylineFileService()
	flightTaskSvc := NewFightTaskService()
	inspectionJobSvc := NewInspectionJobService()
	dockDRCSvc := NewDockDRCService()
	dockControlSvc := NewDockControlService()
	liveStreamSvc := NewLiveStreamService()
	payloadSvc := NewPayloadService()
	wsManager := repo.NewWsManager()

	connID := fmt.Sprintf("%s-%s", fileds.ServerRealTimeControl, gatewaySn)

	isReleaseLock = true

	// 1.获取状态（行为序列、序列阶段、巡检任务信息）
	inspectionStatus, err := statusSvc.getInspectionStatus(gatewaySn)
	if err != nil {
		return isReleaseLock, err
	}

	cid := inspectionStatus.Cid
	uid := inspectionStatus.Uid

	doNext := false

	// 2.根据状态的信息执行相应行为并流转状态
	switch inspectionStatus.Status.CurrentStatus {
	case enum.InspectionStatusEnum_init:
		// 任务初始化
		/*		if inspectionStatus.Status.CurrentStep != nil {
					switch *inspectionStatus.Status.CurrentStep {
					case enum.InspectionStatusStepEnum_Doing:
						// doing：请求算法服务开始本次巡检任务
						var gatewayOnline *dto.DeviceDTO
						gatewayOnline, err = deviceRedisSvc.getDeviceOnline(gatewaySn)
						if err != nil {
							return err
						}
						if gatewayOnline == nil {
							return fmt.Errorf("gateway is offline")
						}

						connId := fmt.Sprintf("%s-%s", fileds.ServerRealTimeControl, gatewaySn)
						lockKey := fmt.Sprintf(fileds.RealTimeControlLock, connId)

						err = jobDataProcessorSvc.requestRealTimeControlConnection(gatewayOnline.ChildSN, gatewaySn, lockKey, inspectionStatus.JobID, connId)
						if err != nil {
							return err
						}

						doNextBehavior = true
				}
				}*/
		doNext = true

	case enum.InspectionStatusEnum_Waiting:
		// 任务执行中-等待状态流转
		doNext = true

	case enum.InspectionStatusEnum_Wayline:
		// 任务执行中-航线飞行
		// 退出来源：航线进度上报。然后要接管。更新为after
		switch inspectionStatus.Status.CurrentStep {
		case enum.InspectionStatusStepEnum_Before:
			// 下发航线任务
			var jobId string
			var WaylineManageID int

			behavior := inspectionStatus.Behavior.BehaviorSequence[*inspectionStatus.Behavior.BehaviorSequenceIndex]
			if behavior.Type == enum.InspectionBehavior_InGroundWaylineDeliver {
				// 下发地面航线
				inGroundWaylineDeliverReq := behavior.BehaviorData.InGroundWaylineDeliverReq
				if inGroundWaylineDeliverReq == nil {
					return isReleaseLock, fmt.Errorf("InGroundWaylineDeliverReq is nil")
				}

				waylineManageIDList := []int{inGroundWaylineDeliverReq.WaylineManageID}
				waylineManageIDListByte, err := json.Marshal(waylineManageIDList)
				if err != nil {
					return isReleaseLock, err
				}

				var aiModelList []string
				aiModelListByte, err := json.Marshal(aiModelList)
				if err != nil {
					return isReleaseLock, err
				}

				var algorithmTarget []string
				algorithmTargetByte, err := json.Marshal(algorithmTarget)
				if err != nil {
					return isReleaseLock, err
				}

				createReq := dto.InspectionJobCreateReq{
					WaylineManageIDList: string(waylineManageIDListByte),
					AIModelList:         string(aiModelListByte),
					Type:                2,
					Name:                "智能巡检",
					MinBatteryCapacity:  inGroundWaylineDeliverReq.MinBatteryCapacity,
					DockSN:              gatewaySn,
					ExecuteType:         1,
					RTHAltitude:         inGroundWaylineDeliverReq.RthAltitude,
					LostContactAction:   1,
					AlgorithmTarget:     string(algorithmTargetByte),
					Scene:               2,
				}

				resp, err, _ := inspectionJobSvc.Create(&createReq, cid, uid)
				if err != nil {
					return isReleaseLock, err
				}

				jobId = resp.WaylineJobId
				WaylineManageID = inGroundWaylineDeliverReq.WaylineManageID

				s.logger.Infof("do InGroundWaylineDeliver success, jobId: %s", resp.WaylineJobId)

			} else if behavior.Type == enum.InspectionBehavior_InFlightWaylineDeliver {
				// 下发空中航线
				if behavior.BehaviorData.InFlightWaylineDeliverReq == nil {
					return isReleaseLock, fmt.Errorf("InFlightWaylineDeliverReq is nil")
				}

				waylineManageID := behavior.BehaviorData.InFlightWaylineDeliverReq.WaylineManageID

				var waylineManage *model.WaylineManage
				waylineManage, err = waylineManageSvc.GetWaylineManageById(waylineManageID)
				if err != nil {
					return isReleaseLock, err
				}

				var waylineFile *model.WaylineFile
				waylineFile, err = waylineFileSvc.GetWaylineById(waylineManage.WaylineFileID)
				if err != nil {
					return isReleaseLock, err
				}

				inFlightJobId, code, err := flightTaskSvc.InFlightWaylineDeliver(gatewaySn, waylineFile.ObjectKey, behavior.BehaviorData.InFlightWaylineDeliverReq.RthAltitude, waylineManageID)
				if err != nil {
					if code != nil {
						s.logger.Errorf("InFlightWaylineDeliver failed, gatewaySn: %s, err: %v, code: %d", gatewaySn, err, *code)
					} else {
						s.logger.Errorf("InFlightWaylineDeliver failed, gatewaySn: %s, err: %v", gatewaySn, err)
					}
					return isReleaseLock, err
				}

				jobId = inFlightJobId
				WaylineManageID = waylineManageID

				s.logger.Infof("do InFlightWaylineDeliver success, inFlightWaylineId: %s", inFlightJobId)
			}

			if jobId != "" {
				inspectionStatus.CurrentWayline = &dto.CurrentWayline{
					WaylineManageID: WaylineManageID,
					JobId:           jobId,
				}
				err = inspectionStatusSvc.setInspectionStatus(gatewaySn, inspectionStatus)
				if err != nil {
					return isReleaseLock, err
				}
			}

			doNext = true

		case enum.InspectionStatusStepEnum_Doing:
			// 航线执行中
			isReleaseLock = false

		case enum.InspectionStatusStepEnum_After:
			if inspectionStatus.CurrentWayline != nil {
				waylineJobId := inspectionStatus.CurrentWayline.JobId

				if inspectionStatus.Behavior.BehaviorSequenceIndex != nil && *inspectionStatus.Behavior.BehaviorSequenceIndex < len(inspectionStatus.Behavior.BehaviorSequence) {
					behavior := inspectionStatus.Behavior.BehaviorSequence[*inspectionStatus.Behavior.BehaviorSequenceIndex]
					if behavior.Type == enum.InspectionBehavior_InGroundWaylineDeliver && behavior.BehaviorData.InGroundWaylineDeliverReq != nil {
						// 地面航线
						if behavior.BehaviorData.InGroundWaylineDeliverReq.TargetPoint != nil {
							// 到达中途目标点，暂停航线、取消航线
							_, err, errMsg := flightTaskSvc.UpdateFlightTaskStatus(waylineJobId, enum.WaylineTaskStatus_PAUSED)
							if err != nil {
								s.logger.Errorf("FlighttaskPause failed, jobId: %s, err: %v, errMsg: %s", waylineJobId, err, errMsg)
								return isReleaseLock, err
							}

							time.Sleep(200 * time.Millisecond)

							err = flightTaskSvc.CancelFlightTask(gatewaySn, waylineJobId)
							if err != nil {
								s.logger.Errorf("cancel flight task failed, jobId: %s, err: %v", waylineJobId, err)
								return isReleaseLock, err
							}
						}
						// 到达终点，自动悬停
					} else if behavior.Type == enum.InspectionBehavior_InFlightWaylineDeliver && behavior.BehaviorData.InFlightWaylineDeliverReq != nil {
						// 空中航线
						if behavior.BehaviorData.InGroundWaylineDeliverReq.TargetPoint != nil {
							// 到达中途目标点，暂停航线、取消航线
							err = flightTaskSvc.InFlightWaylineStop(gatewaySn, waylineJobId)
							if err != nil {
								s.logger.Errorf("InFlightWaylineStop failed, jobId: %s, err: %v", waylineJobId, err)
								return isReleaseLock, err
							}

							time.Sleep(200 * time.Millisecond)

							err = flightTaskSvc.InFlightWaylineCancel(gatewaySn, waylineJobId)
							if err != nil {
								s.logger.Errorf("cancel flight task failed, jobId: %s, err: %v", waylineJobId, err)
								return isReleaseLock, err
							}
						}
						// 到达终点，自动悬停
					}
				}
			}

			doNext = true
		}

	case enum.InspectionStatusEnum_ManualControl:
		// 执行中-预设指令巡检
		if inspectionStatus.Status.CurrentStep == enum.InspectionStatusStepEnum_Before {
			// 接管
			gatewayOnline, err := deviceRedisSvc.getDeviceOnline(gatewaySn)
			if err != nil {
				return isReleaseLock, err
			}
			if gatewayOnline == nil {
				return isReleaseLock, fmt.Errorf("gateway is offline")
			}

			droneSn := gatewayOnline.ChildSN

			err, errMsg := NewCockpitFlightService().DroneTakeOver(droneSn)
			if err != nil {
				s.logger.Errorf("drone take over failed, droneSn: %s, err: %v, errMsg: %s", droneSn, err, errMsg)
				return isReleaseLock, err
			}

			// 等待3s
			time.Sleep(3 * time.Second)

			// 顺序执行预设指令
			behavior := inspectionStatus.Behavior.BehaviorSequence[*inspectionStatus.Behavior.BehaviorSequenceIndex]
			manualControlReq := behavior.BehaviorData.ManualControlReq
			if manualControlReq == nil {
				return isReleaseLock, fmt.Errorf("ManualControlReq is nil")
			}

			for _, cmd := range manualControlReq {
				switch cmd.Type {
				case enum.DroneControl:
					// 飞控
					if cmd.DroneControlReq == nil {
						return isReleaseLock, fmt.Errorf("drone control req is nil")
					}

					cmd.DroneControlReq.DockSn = gatewaySn

					err = dockDRCSvc.DeviceDrcCmd(*cmd.DroneControlReq)
					if err != nil {
						s.logger.Errorf("drone control failed, gatewaySn: %s, err: %v", gatewaySn, err)
						return isReleaseLock, err
					}
					s.logger.Infof("drone control success, gatewaySn: %s, x:%f, y:%f, h:%f, w:%f", gatewaySn, cmd.DroneControlReq.X, cmd.DroneControlReq.Y, cmd.DroneControlReq.H, cmd.DroneControlReq.W)

				case enum.PayloadCommand:
					// 云台控制
					if cmd.PayloadCommandsReq == nil {
						return isReleaseLock, fmt.Errorf("payload commands req is nil")
					}

					cmd.PayloadCommandsReq.DockSn = gatewaySn

					_, err, errMsg := dockControlSvc.PayloadCmd(*cmd.PayloadCommandsReq)
					if err != nil {
						s.logger.Errorf("payload cmd failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
						return isReleaseLock, err
					}
					s.logger.Infof("payload cmd success, gatewaySn: %s, method:%s", gatewaySn, cmd.PayloadCommandsReq.Method)

				case enum.LiveLensChange:
					// 变焦
					if cmd.LiveLensChange == nil {
						return isReleaseLock, fmt.Errorf("live lens change req is nil")
					}

					err, errMsg := liveStreamSvc.LiveLensChange(gatewaySn, cmd.LiveLensChange.PayloadIndex, cmd.LiveLensChange.CameraType)
					if err != nil {
						s.logger.Errorf("flightTaskTransfer, LiveLensChange failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
						return isReleaseLock, err
					}

					s.logger.Infof("do LiveLensChange success, gatewaySn: %s, cameraType: %s", gatewaySn, cmd.LiveLensChange.CameraType)

				case enum.CameraScreenDrag:
					// 云台调整
					if cmd.CameraScreenDrag == nil {
						return isReleaseLock, fmt.Errorf("camera screen drag is nil")
					}

					for i := 0; i < cmd.CameraScreenDrag.DoNumber; i++ {
						payloadCmd := dto.PayloadCommandsReq{
							DockSn: gatewaySn,
							Method: fileds.Camera_Screen_Drag,
							Data: device_request.DronePayloadRequest{
								PayloadIndex: cmd.CameraScreenDrag.PayloadIndex,
								Locked:       cmd.CameraScreenDrag.Locked,
								PitchSpeed:   cmd.CameraScreenDrag.PitchSpeed,
								YawSpeed:     cmd.CameraScreenDrag.YawSpeed,
							},
						}

						_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
						if err != nil {
							s.logger.Errorf("camera_screen_drag failed, do_time: %d, pitch_speed: %v, yaw_speed: %v, gatewaySn: %s, err: %v, errMsg: %s", i, cmd.CameraScreenDrag.PitchSpeed, cmd.CameraScreenDrag.YawSpeed, gatewaySn, err, errMsg)
							continue
						} else {
							s.logger.Infof("flightTaskTransfer, camera_screen_drag success, do_time: %d, pitch_speed: %v, yaw_speed: %v, gatewaySn: %s", i, cmd.CameraScreenDrag.PitchSpeed, cmd.CameraScreenDrag.YawSpeed, gatewaySn)
						}

						if cmd.CameraScreenDrag.WaitTime != nil {
							time.Sleep(*cmd.CameraScreenDrag.WaitTime)
						}
					}

				case enum.LightModeSet:
					// 设置探照灯
					if cmd.PayloadLightModeSetReq == nil || cmd.PayloadLightModeSetReq.Mode == nil {
						return isReleaseLock, fmt.Errorf("PayloadLightModeSetReq req is nil")
					}

					cmd.PayloadLightModeSetReq.DockSn = gatewaySn

					err, errMsg := payloadSvc.LightModeSet(*cmd.PayloadLightModeSetReq)
					if err != nil {
						s.logger.Errorf("LightModeSet failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
						continue
					}

					s.logger.Infof("do LightModeSet success, gatewaySn: %s, type: %d", gatewaySn, *cmd.PayloadLightModeSetReq.Mode)

				}

				if cmd.WaitTime != nil {
					time.Sleep(*cmd.WaitTime)
				}
			}
		}

		doNext = true

	case enum.InspectionStatusEnum_AIControl:
		// 任务执行中-算法智能巡检
		// 退出来源：完成该阶段的巡检目标。巡检目标从行为序列中获取
		switch inspectionStatus.Status.CurrentStep {
		case enum.InspectionStatusStepEnum_Before:
			// 接管
			gatewayOnline, err := deviceRedisSvc.getDeviceOnline(gatewaySn)
			if err != nil {
				return isReleaseLock, err
			}
			if gatewayOnline == nil {
				return isReleaseLock, fmt.Errorf("gateway is offline")
			}

			droneSn := gatewayOnline.ChildSN

			err, errMsg := NewCockpitFlightService().DroneTakeOver(droneSn)
			if err != nil {
				s.logger.Errorf("drone take over failed, droneSn: %s, err: %v, errMsg: %s", droneSn, err, errMsg)
				return isReleaseLock, err
			}

			// 等待3s
			time.Sleep(3 * time.Second)

			// 检查websocket连接
			if _, ok := wsManager.WsConn[connID]; !ok {
				s.logger.Errorf("websocket connection not found, connID: %s", connID)
				return isReleaseLock, err
			}

			// 云台回中
			_, err, errMsg = dockControlSvc.PayloadCmd(dto.PayloadCommandsReq{
				Method: fileds.GIMBAL_RESET,
				Data: device_request.DronePayloadRequest{
					PayloadIndex: "99-0-0",
					ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
				},
			})
			if err != nil {
				s.logger.Errorf("payload cmd failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
				return isReleaseLock, err
			}

			// 切变焦
			err, errMsg = liveStreamSvc.LiveLensChange(gatewaySn, "99-0-0", live_stream_cloud_enum.LensChangeVideo_ZOOM)
			if err != nil {
				s.logger.Errorf("LiveLensChange failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
				return isReleaseLock, err
			}

			// 变焦1x
			_, err, errMsg = dockControlSvc.PayloadCmd(dto.PayloadCommandsReq{
				Method: fileds.CAMERA_FOCAL_LENGTH_SET,
				Data: device_request.DronePayloadRequest{
					PayloadIndex: "99-0-0",
					CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
					ZoomFactor:   utils.Float64Ptr(1),
				},
			})
			if err != nil {
				s.logger.Errorf("payload cmd failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
				return isReleaseLock, err
			}

			// 控制权转交给算法
			req := dto.JobDataProcessingReq{
				Type: fileds.RealTimeControlReq,
				Data: dto.RealTimeControlReq{
					DepthControlEnabled: true,
				},
			}

			bytes, err := json.Marshal(&req)
			if err != nil {
				s.logger.Errorf("failed to encode data, err: %v", err)
				return isReleaseLock, err
			}

			wsManager.SendData(bytes, connID)

			// 同步更改状态
			inspectionStatus.DepthControlEnabled = true
			err = inspectionStatusSvc.setInspectionStatus(gatewaySn, inspectionStatus)
			if err != nil {
				return isReleaseLock, err
			}

			doNext = true

		case enum.InspectionStatusStepEnum_Doing:
			// 算法智能巡检中
			isReleaseLock = false

		case enum.InspectionStatusStepEnum_After:
			// 切变焦
			err, errMsg := liveStreamSvc.LiveLensChange(gatewaySn, "99-0-0", live_stream_cloud_enum.LensChangeVideo_WIDE)
			if err != nil {
				s.logger.Errorf("LiveLensChange failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
				return isReleaseLock, err
			}

			// 云台回中
			_, err, errMsg = dockControlSvc.PayloadCmd(dto.PayloadCommandsReq{
				Method: fileds.GIMBAL_RESET,
				Data: device_request.DronePayloadRequest{
					PayloadIndex: "99-0-0",
					ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
				},
			})
			if err != nil {
				s.logger.Errorf("payload cmd failed, gatewaySn: %s, err: %v, errMsg: %s", gatewaySn, err, errMsg)
				return isReleaseLock, err
			}

			doNext = true
		}

	case enum.InspectionStatusEnum_ReturnHome:
		// 任务执行中-安全返航
		// 进入来源：行为序列、算法决策
		// 退出来源：航线进度上报

		switch inspectionStatus.Status.CurrentStep {
		case enum.InspectionStatusStepEnum_Before:
			// todo v3 执行返航。需要设施信息、航线轨迹信息
			// fixme 目前v1先直接下发空中航线
			waylineManageID := 220
			rthAltitude := 50

			var waylineManage *model.WaylineManage
			waylineManage, err = waylineManageSvc.GetWaylineManageById(waylineManageID)
			if err != nil {
				return isReleaseLock, err
			}

			var waylineFile *model.WaylineFile
			waylineFile, err = waylineFileSvc.GetWaylineById(waylineManage.WaylineFileID)
			if err != nil {
				return isReleaseLock, err
			}

			inFlightJobId, code, err := flightTaskSvc.InFlightWaylineDeliver(gatewaySn, waylineFile.ObjectKey, rthAltitude, waylineManageID)
			if err != nil {
				if code != nil {
					s.logger.Errorf("returnHome: InFlightWaylineDeliver failed, gatewaySn: %s, err: %v, code: %d", gatewaySn, err, *code)
				} else {
					s.logger.Errorf("returnHome: InFlightWaylineDeliver failed, gatewaySn: %s, err: %v", gatewaySn, err)
				}
				return isReleaseLock, err
			}

			s.logger.Infof("returnHome: do InFlightWaylineDeliver success, inFlightWaylineId: %s", inFlightJobId)

			doNext = true

		case enum.InspectionStatusStepEnum_Doing:
			// 返航中
			isReleaseLock = false

		case enum.InspectionStatusStepEnum_After:
			// todo v3 后续设施巡检业务定了，这里要结束设施巡检任务
			err = inspectionStatusService.delInspectionStatus(gatewaySn)
			if err != nil {
				return isReleaseLock, err
			}
		}

	default:
	}

	if doNext {
		// 读取行为序列，流转下一个状态
		behaviorSequenceIndex := inspectionStatus.Behavior.BehaviorSequenceIndex
		currentStatus := inspectionStatus.Status.CurrentStatus
		statusStep := inspectionStatus.Status.CurrentStep

		if statusStep == enum.InspectionStatusStepEnum_Before {
			statusStep = enum.InspectionStatusStepEnum_Doing
		} else if statusStep == enum.InspectionStatusStepEnum_Doing {
			statusStep = enum.InspectionStatusStepEnum_After
		} else if statusStep == enum.InspectionStatusStepEnum_After {
			// after结束了，流转下一个行为
			// 行为序列索引+1
			if behaviorSequenceIndex != nil {
				if *behaviorSequenceIndex < len(inspectionStatus.Behavior.BehaviorSequence)-1 {
					behaviorSequenceIndex = utils.IntPtr(*inspectionStatus.Behavior.BehaviorSequenceIndex + 1)
				}
			} else {
				behaviorSequenceIndex = utils.IntPtr(0)
			}

			// 设置状态
			if *behaviorSequenceIndex > len(inspectionStatus.Behavior.BehaviorSequence)-1 {
				// 行为序列结束，设置为返航
				// 行为序列最后一个动作本来就应该是返航，返航结束了会结束任务，所以一般不会走到这一步
				// todo 返航或结束任务
				currentStatus = enum.InspectionBehavior_ReturnHome
			} else {
				// 行为序列未结束，设置为下一个行为的状态
				behavior := inspectionStatus.Behavior.BehaviorSequence[*behaviorSequenceIndex].Type
				switch behavior {
				case enum.InspectionBehavior_ManualControl:
					currentStatus = enum.InspectionStatusEnum_ManualControl
				case enum.InspectionBehavior_InGroundWaylineDeliver:
					currentStatus = enum.InspectionStatusEnum_Wayline
				case enum.InspectionBehavior_InFlightWaylineDeliver:
					currentStatus = enum.InspectionStatusEnum_Wayline
				case enum.InspectionBehavior_AlgorithmControl:
					currentStatus = enum.InspectionStatusEnum_AIControl
				case enum.InspectionBehavior_ReturnHome:
					currentStatus = enum.InspectionStatusEnum_ReturnHome
				default:
					s.logger.Errorf("behavior: %s, unhandled default case", behavior.String())
				}
			}

			// 状态阶段重置为before
			statusStep = enum.InspectionStatusStepEnum_Before
		}

		status := dto.InspectionStatus{
			Cid:       inspectionStatus.Cid,
			Uid:       inspectionStatus.Uid,
			GatewaySn: inspectionStatus.GatewaySn,
			JobID:     inspectionStatus.JobID,
			Behavior: dto.Behavior{
				BehaviorSequence:      inspectionStatus.Behavior.BehaviorSequence,
				BehaviorSequenceIndex: behaviorSequenceIndex,
			},
			Status: dto.Status{
				CurrentStatus: currentStatus,
				CurrentStep:   statusStep,
			},
		}

		err = inspectionStatusSvc.setInspectionStatus(gatewaySn, &status)
		if err != nil {
			return isReleaseLock, err
		}
	}

	return isReleaseLock, nil
}
