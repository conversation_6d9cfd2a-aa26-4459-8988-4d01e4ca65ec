// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v4.24.1
// source: device_control.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	DeviceControlService_DeviceControl_FullMethodName = "/main.DeviceControlService/DeviceControl"
)

// DeviceControlServiceClient is the client API for DeviceControlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeviceControlServiceClient interface {
	DeviceControl(ctx context.Context, in *DeviceControlRequest, opts ...grpc.CallOption) (*DeviceControlResponse, error)
}

type deviceControlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceControlServiceClient(cc grpc.ClientConnInterface) DeviceControlServiceClient {
	return &deviceControlServiceClient{cc}
}

func (c *deviceControlServiceClient) DeviceControl(ctx context.Context, in *DeviceControlRequest, opts ...grpc.CallOption) (*DeviceControlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceControlResponse)
	err := c.cc.Invoke(ctx, DeviceControlService_DeviceControl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceControlServiceServer is the server API for DeviceControlService service.
// All implementations must embed UnimplementedDeviceControlServiceServer
// for forward compatibility
type DeviceControlServiceServer interface {
	DeviceControl(context.Context, *DeviceControlRequest) (*DeviceControlResponse, error)
	mustEmbedUnimplementedDeviceControlServiceServer()
}

// UnimplementedDeviceControlServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDeviceControlServiceServer struct {
}

func (UnimplementedDeviceControlServiceServer) DeviceControl(context.Context, *DeviceControlRequest) (*DeviceControlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceControl not implemented")
}
func (UnimplementedDeviceControlServiceServer) mustEmbedUnimplementedDeviceControlServiceServer() {}

// UnsafeDeviceControlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceControlServiceServer will
// result in compilation errors.
type UnsafeDeviceControlServiceServer interface {
	mustEmbedUnimplementedDeviceControlServiceServer()
}

func RegisterDeviceControlServiceServer(s grpc.ServiceRegistrar, srv DeviceControlServiceServer) {
	s.RegisterService(&DeviceControlService_ServiceDesc, srv)
}

func _DeviceControlService_DeviceControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceControlServiceServer).DeviceControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceControlService_DeviceControl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceControlServiceServer).DeviceControl(ctx, req.(*DeviceControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DeviceControlService_ServiceDesc is the grpc.ServiceDesc for DeviceControlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeviceControlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "main.DeviceControlService",
	HandlerType: (*DeviceControlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DeviceControl",
			Handler:    _DeviceControlService_DeviceControl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "device_control.proto",
}
