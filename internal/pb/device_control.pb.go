// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.24.1
// source: proto/device_control.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 操作类型枚举
type ActionType int32

const (
	ActionType_ACTION_TYPE_UNSPECIFIED ActionType = 0 // 未指定
	ActionType_ACTION_TYPE_DRONE       ActionType = 1 // 无人机控制
	ActionType_ACTION_TYPE_GIMBAL      ActionType = 2 // 云台控制
	ActionType_ACTION_TYPE_PHOTO       ActionType = 3 // 拍照
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "ACTION_TYPE_DRONE",
		2: "ACTION_TYPE_GIMBAL",
		3: "ACTION_TYPE_PHOTO",
	}
	ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED": 0,
		"ACTION_TYPE_DRONE":       1,
		"ACTION_TYPE_GIMBAL":      2,
		"ACTION_TYPE_PHOTO":       3,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_device_control_proto_enumTypes[0].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_proto_device_control_proto_enumTypes[0]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{0}
}

// 变焦类型枚举
type ZoomType int32

const (
	ZoomType_ZOOM_TYPE_UNSPECIFIED ZoomType = 0 // 未指定
	ZoomType_ZOOM_TYPE_IN          ZoomType = 1 // 拉近焦距
	ZoomType_ZOOM_TYPE_OUT         ZoomType = 2 // 拉远焦距
)

// Enum value maps for ZoomType.
var (
	ZoomType_name = map[int32]string{
		0: "ZOOM_TYPE_UNSPECIFIED",
		1: "ZOOM_TYPE_IN",
		2: "ZOOM_TYPE_OUT",
	}
	ZoomType_value = map[string]int32{
		"ZOOM_TYPE_UNSPECIFIED": 0,
		"ZOOM_TYPE_IN":          1,
		"ZOOM_TYPE_OUT":         2,
	}
)

func (x ZoomType) Enum() *ZoomType {
	p := new(ZoomType)
	*p = x
	return p
}

func (x ZoomType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ZoomType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_device_control_proto_enumTypes[1].Descriptor()
}

func (ZoomType) Type() protoreflect.EnumType {
	return &file_proto_device_control_proto_enumTypes[1]
}

func (x ZoomType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ZoomType.Descriptor instead.
func (ZoomType) EnumDescriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{1}
}

// 云台类型枚举
type GimbalType int32

const (
	GimbalType_GIMBAL_TYPE_UNSPECIFIED GimbalType = 0 // 未指定
	GimbalType_GIMBAL_TYPE_ZOOM        GimbalType = 1 // 变焦
	GimbalType_GIMBAL_TYPE_WIDE        GimbalType = 2 // 广角
)

// Enum value maps for GimbalType.
var (
	GimbalType_name = map[int32]string{
		0: "GIMBAL_TYPE_UNSPECIFIED",
		1: "GIMBAL_TYPE_ZOOM",
		2: "GIMBAL_TYPE_WIDE",
	}
	GimbalType_value = map[string]int32{
		"GIMBAL_TYPE_UNSPECIFIED": 0,
		"GIMBAL_TYPE_ZOOM":        1,
		"GIMBAL_TYPE_WIDE":        2,
	}
)

func (x GimbalType) Enum() *GimbalType {
	p := new(GimbalType)
	*p = x
	return p
}

func (x GimbalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GimbalType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_device_control_proto_enumTypes[2].Descriptor()
}

func (GimbalType) Type() protoreflect.EnumType {
	return &file_proto_device_control_proto_enumTypes[2]
}

func (x GimbalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GimbalType.Descriptor instead.
func (GimbalType) EnumDescriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{2}
}

type DeviceControlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionType    ActionType     `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=main.ActionType" json:"action_type,omitempty"`
	DockSn        string         `protobuf:"bytes,2,opt,name=dock_sn,json=dockSn,proto3" json:"dock_sn,omitempty"`
	DroneSn       string         `protobuf:"bytes,3,opt,name=drone_sn,json=droneSn,proto3" json:"drone_sn,omitempty"`
	DroneControl  *DroneControl  `protobuf:"bytes,4,opt,name=drone_control,json=droneControl,proto3" json:"drone_control,omitempty"`
	GimbalControl *GimbalControl `protobuf:"bytes,5,opt,name=gimbal_control,json=gimbalControl,proto3" json:"gimbal_control,omitempty"`
}

func (x *DeviceControlRequest) Reset() {
	*x = DeviceControlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_device_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceControlRequest) ProtoMessage() {}

func (x *DeviceControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_device_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceControlRequest.ProtoReflect.Descriptor instead.
func (*DeviceControlRequest) Descriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceControlRequest) GetActionType() ActionType {
	if x != nil {
		return x.ActionType
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *DeviceControlRequest) GetDockSn() string {
	if x != nil {
		return x.DockSn
	}
	return ""
}

func (x *DeviceControlRequest) GetDroneSn() string {
	if x != nil {
		return x.DroneSn
	}
	return ""
}

func (x *DeviceControlRequest) GetDroneControl() *DroneControl {
	if x != nil {
		return x.DroneControl
	}
	return nil
}

func (x *DeviceControlRequest) GetGimbalControl() *GimbalControl {
	if x != nil {
		return x.GimbalControl
	}
	return nil
}

type DeviceControlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Succeed bool `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
}

func (x *DeviceControlResponse) Reset() {
	*x = DeviceControlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_device_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceControlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceControlResponse) ProtoMessage() {}

func (x *DeviceControlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_device_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceControlResponse.ProtoReflect.Descriptor instead.
func (*DeviceControlResponse) Descriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceControlResponse) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type DroneControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float64 `protobuf:"fixed64,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float64 `protobuf:"fixed64,2,opt,name=y,proto3" json:"y,omitempty"`
	H float64 `protobuf:"fixed64,3,opt,name=h,proto3" json:"h,omitempty"`
	W float64 `protobuf:"fixed64,4,opt,name=w,proto3" json:"w,omitempty"`
}

func (x *DroneControl) Reset() {
	*x = DroneControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_device_control_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneControl) ProtoMessage() {}

func (x *DroneControl) ProtoReflect() protoreflect.Message {
	mi := &file_proto_device_control_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneControl.ProtoReflect.Descriptor instead.
func (*DroneControl) Descriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{2}
}

func (x *DroneControl) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *DroneControl) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *DroneControl) GetH() float64 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *DroneControl) GetW() float64 {
	if x != nil {
		return x.W
	}
	return 0
}

type GimbalControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PitchAdjust float64    `protobuf:"fixed64,1,opt,name=pitch_adjust,json=pitchAdjust,proto3" json:"pitch_adjust,omitempty"`
	YawAdjust   float64    `protobuf:"fixed64,2,opt,name=yaw_adjust,json=yawAdjust,proto3" json:"yaw_adjust,omitempty"`
	ZoomType    ZoomType   `protobuf:"varint,3,opt,name=zoom_type,json=zoomType,proto3,enum=main.ZoomType" json:"zoom_type,omitempty"`
	GimbalType  GimbalType `protobuf:"varint,4,opt,name=gimbal_type,json=gimbalType,proto3,enum=main.GimbalType" json:"gimbal_type,omitempty"`
}

func (x *GimbalControl) Reset() {
	*x = GimbalControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_device_control_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GimbalControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GimbalControl) ProtoMessage() {}

func (x *GimbalControl) ProtoReflect() protoreflect.Message {
	mi := &file_proto_device_control_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GimbalControl.ProtoReflect.Descriptor instead.
func (*GimbalControl) Descriptor() ([]byte, []int) {
	return file_proto_device_control_proto_rawDescGZIP(), []int{3}
}

func (x *GimbalControl) GetPitchAdjust() float64 {
	if x != nil {
		return x.PitchAdjust
	}
	return 0
}

func (x *GimbalControl) GetYawAdjust() float64 {
	if x != nil {
		return x.YawAdjust
	}
	return 0
}

func (x *GimbalControl) GetZoomType() ZoomType {
	if x != nil {
		return x.ZoomType
	}
	return ZoomType_ZOOM_TYPE_UNSPECIFIED
}

func (x *GimbalControl) GetGimbalType() GimbalType {
	if x != nil {
		return x.GimbalType
	}
	return GimbalType_GIMBAL_TYPE_UNSPECIFIED
}

var File_proto_device_control_proto protoreflect.FileDescriptor

var file_proto_device_control_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x6d, 0x61,
	0x69, 0x6e, 0x22, 0xf2, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x10, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x64, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x6f, 0x63, 0x6b, 0x53, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x72, 0x6f, 0x6e, 0x65,
	0x5f, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x72, 0x6f, 0x6e, 0x65,
	0x53, 0x6e, 0x12, 0x37, 0x0a, 0x0d, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x61, 0x69, 0x6e,
	0x2e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0c, 0x64,
	0x72, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x3a, 0x0a, 0x0e, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x69, 0x6d, 0x62, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0d, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x22, 0x31, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x22, 0x46, 0x0a, 0x0c, 0x44, 0x72,
	0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x01, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x01, 0x77, 0x22, 0xb1, 0x01, 0x0a, 0x0d, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x70, 0x69, 0x74, 0x63,
	0x68, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x79, 0x61, 0x77, 0x5f, 0x61,
	0x64, 0x6a, 0x75, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x79, 0x61, 0x77,
	0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x7a, 0x6f, 0x6f, 0x6d, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x6d, 0x61, 0x69, 0x6e,
	0x2e, 0x5a, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x7a, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x0b, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e,
	0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x67, 0x69, 0x6d, 0x62,
	0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x6f, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x44, 0x52, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x49, 0x4d, 0x42, 0x41, 0x4c, 0x10, 0x02,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x48, 0x4f, 0x54, 0x4f, 0x10, 0x03, 0x2a, 0x4a, 0x0a, 0x08, 0x5a, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x5a, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x5a, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x10, 0x01,
	0x12, 0x11, 0x0a, 0x0d, 0x5a, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x55,
	0x54, 0x10, 0x02, 0x2a, 0x55, 0x0a, 0x0a, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x49, 0x4d, 0x42, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x47, 0x49, 0x4d, 0x42, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x5a, 0x4f,
	0x4f, 0x4d, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x49, 0x4d, 0x42, 0x41, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x44, 0x45, 0x10, 0x02, 0x32, 0x62, 0x0a, 0x14, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x12, 0x1a, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x10,
	0x5a, 0x0e, 0x2e, 0x2e, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_device_control_proto_rawDescOnce sync.Once
	file_proto_device_control_proto_rawDescData = file_proto_device_control_proto_rawDesc
)

func file_proto_device_control_proto_rawDescGZIP() []byte {
	file_proto_device_control_proto_rawDescOnce.Do(func() {
		file_proto_device_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_device_control_proto_rawDescData)
	})
	return file_proto_device_control_proto_rawDescData
}

var file_proto_device_control_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_device_control_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_device_control_proto_goTypes = []any{
	(ActionType)(0),               // 0: main.ActionType
	(ZoomType)(0),                 // 1: main.ZoomType
	(GimbalType)(0),               // 2: main.GimbalType
	(*DeviceControlRequest)(nil),  // 3: main.DeviceControlRequest
	(*DeviceControlResponse)(nil), // 4: main.DeviceControlResponse
	(*DroneControl)(nil),          // 5: main.DroneControl
	(*GimbalControl)(nil),         // 6: main.GimbalControl
}
var file_proto_device_control_proto_depIdxs = []int32{
	0, // 0: main.DeviceControlRequest.action_type:type_name -> main.ActionType
	5, // 1: main.DeviceControlRequest.drone_control:type_name -> main.DroneControl
	6, // 2: main.DeviceControlRequest.gimbal_control:type_name -> main.GimbalControl
	1, // 3: main.GimbalControl.zoom_type:type_name -> main.ZoomType
	2, // 4: main.GimbalControl.gimbal_type:type_name -> main.GimbalType
	3, // 5: main.DeviceControlService.DeviceControl:input_type -> main.DeviceControlRequest
	4, // 6: main.DeviceControlService.DeviceControl:output_type -> main.DeviceControlResponse
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_device_control_proto_init() }
func file_proto_device_control_proto_init() {
	if File_proto_device_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_device_control_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*DeviceControlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_device_control_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*DeviceControlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_device_control_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*DroneControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_device_control_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GimbalControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_device_control_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_device_control_proto_goTypes,
		DependencyIndexes: file_proto_device_control_proto_depIdxs,
		EnumInfos:         file_proto_device_control_proto_enumTypes,
		MessageInfos:      file_proto_device_control_proto_msgTypes,
	}.Build()
	File_proto_device_control_proto = out.File
	file_proto_device_control_proto_rawDesc = nil
	file_proto_device_control_proto_goTypes = nil
	file_proto_device_control_proto_depIdxs = nil
}
