// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.24.1
// source: device_control.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceControlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionType    string         `protobuf:"bytes,1,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	DockSn        string         `protobuf:"bytes,2,opt,name=dock_sn,json=dockSn,proto3" json:"dock_sn,omitempty"`
	DroneSn       string         `protobuf:"bytes,3,opt,name=drone_sn,json=droneSn,proto3" json:"drone_sn,omitempty"`
	DroneControl  *DroneControl  `protobuf:"bytes,4,opt,name=drone_control,json=droneControl,proto3" json:"drone_control,omitempty"`
	GimbalControl *GimbalControl `protobuf:"bytes,5,opt,name=gimbal_control,json=gimbalControl,proto3" json:"gimbal_control,omitempty"`
}

func (x *DeviceControlRequest) Reset() {
	*x = DeviceControlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_device_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceControlRequest) ProtoMessage() {}

func (x *DeviceControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_device_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceControlRequest.ProtoReflect.Descriptor instead.
func (*DeviceControlRequest) Descriptor() ([]byte, []int) {
	return file_device_control_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceControlRequest) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *DeviceControlRequest) GetDockSn() string {
	if x != nil {
		return x.DockSn
	}
	return ""
}

func (x *DeviceControlRequest) GetDroneSn() string {
	if x != nil {
		return x.DroneSn
	}
	return ""
}

func (x *DeviceControlRequest) GetDroneControl() *DroneControl {
	if x != nil {
		return x.DroneControl
	}
	return nil
}

func (x *DeviceControlRequest) GetGimbalControl() *GimbalControl {
	if x != nil {
		return x.GimbalControl
	}
	return nil
}

type DeviceControlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Succeed bool `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
}

func (x *DeviceControlResponse) Reset() {
	*x = DeviceControlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_device_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceControlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceControlResponse) ProtoMessage() {}

func (x *DeviceControlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_device_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceControlResponse.ProtoReflect.Descriptor instead.
func (*DeviceControlResponse) Descriptor() ([]byte, []int) {
	return file_device_control_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceControlResponse) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type DroneControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float64 `protobuf:"fixed64,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float64 `protobuf:"fixed64,2,opt,name=y,proto3" json:"y,omitempty"`
	H float64 `protobuf:"fixed64,3,opt,name=h,proto3" json:"h,omitempty"`
	W float64 `protobuf:"fixed64,4,opt,name=w,proto3" json:"w,omitempty"`
}

func (x *DroneControl) Reset() {
	*x = DroneControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_device_control_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneControl) ProtoMessage() {}

func (x *DroneControl) ProtoReflect() protoreflect.Message {
	mi := &file_device_control_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneControl.ProtoReflect.Descriptor instead.
func (*DroneControl) Descriptor() ([]byte, []int) {
	return file_device_control_proto_rawDescGZIP(), []int{2}
}

func (x *DroneControl) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *DroneControl) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *DroneControl) GetH() float64 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *DroneControl) GetW() float64 {
	if x != nil {
		return x.W
	}
	return 0
}

type GimbalControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PitchAdjust float64 `protobuf:"fixed64,1,opt,name=pitch_adjust,json=pitchAdjust,proto3" json:"pitch_adjust,omitempty"`
	YawAdjust   float64 `protobuf:"fixed64,2,opt,name=yaw_adjust,json=yawAdjust,proto3" json:"yaw_adjust,omitempty"`
	ZoomType    int32   `protobuf:"varint,3,opt,name=zoom_type,json=zoomType,proto3" json:"zoom_type,omitempty"`
	GimbalType  int32   `protobuf:"varint,4,opt,name=gimbal_type,json=gimbalType,proto3" json:"gimbal_type,omitempty"`
}

func (x *GimbalControl) Reset() {
	*x = GimbalControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_device_control_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GimbalControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GimbalControl) ProtoMessage() {}

func (x *GimbalControl) ProtoReflect() protoreflect.Message {
	mi := &file_device_control_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GimbalControl.ProtoReflect.Descriptor instead.
func (*GimbalControl) Descriptor() ([]byte, []int) {
	return file_device_control_proto_rawDescGZIP(), []int{3}
}

func (x *GimbalControl) GetPitchAdjust() float64 {
	if x != nil {
		return x.PitchAdjust
	}
	return 0
}

func (x *GimbalControl) GetYawAdjust() float64 {
	if x != nil {
		return x.YawAdjust
	}
	return 0
}

func (x *GimbalControl) GetZoomType() int32 {
	if x != nil {
		return x.ZoomType
	}
	return 0
}

func (x *GimbalControl) GetGimbalType() int32 {
	if x != nil {
		return x.GimbalType
	}
	return 0
}

var File_device_control_proto protoreflect.FileDescriptor

var file_device_control_proto_rawDesc = []byte{
	0x0a, 0x14, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0xe0, 0x01, 0x0a,
	0x14, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x6f, 0x63, 0x6b, 0x5f, 0x73,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x6b, 0x53, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x53, 0x6e, 0x12, 0x37, 0x0a, 0x0d, 0x64, 0x72,
	0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0c, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x12, 0x3a, 0x0a, 0x0e, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x61,
	0x69, 0x6e, 0x2e, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x52, 0x0d, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x22,
	0x31, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x65, 0x64, 0x22, 0x46, 0x0a, 0x0c, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x78,
	0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x79, 0x12, 0x0c,
	0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a, 0x01,
	0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x77, 0x22, 0x8f, 0x01, 0x0a, 0x0d, 0x47,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x70, 0x69, 0x74, 0x63, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x79, 0x61, 0x77, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x79, 0x61, 0x77, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x7a, 0x6f, 0x6f, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x7a, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x32, 0x62, 0x0a, 0x14,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x1a, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x10, 0x5a, 0x0e, 0x2e, 0x2e, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_device_control_proto_rawDescOnce sync.Once
	file_device_control_proto_rawDescData = file_device_control_proto_rawDesc
)

func file_device_control_proto_rawDescGZIP() []byte {
	file_device_control_proto_rawDescOnce.Do(func() {
		file_device_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_device_control_proto_rawDescData)
	})
	return file_device_control_proto_rawDescData
}

var file_device_control_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_device_control_proto_goTypes = []any{
	(*DeviceControlRequest)(nil),  // 0: main.DeviceControlRequest
	(*DeviceControlResponse)(nil), // 1: main.DeviceControlResponse
	(*DroneControl)(nil),          // 2: main.DroneControl
	(*GimbalControl)(nil),         // 3: main.GimbalControl
}
var file_device_control_proto_depIdxs = []int32{
	2, // 0: main.DeviceControlRequest.drone_control:type_name -> main.DroneControl
	3, // 1: main.DeviceControlRequest.gimbal_control:type_name -> main.GimbalControl
	0, // 2: main.DeviceControlService.DeviceControl:input_type -> main.DeviceControlRequest
	1, // 3: main.DeviceControlService.DeviceControl:output_type -> main.DeviceControlResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_device_control_proto_init() }
func file_device_control_proto_init() {
	if File_device_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_device_control_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*DeviceControlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_device_control_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*DeviceControlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_device_control_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*DroneControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_device_control_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GimbalControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_device_control_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_device_control_proto_goTypes,
		DependencyIndexes: file_device_control_proto_depIdxs,
		MessageInfos:      file_device_control_proto_msgTypes,
	}.Build()
	File_device_control_proto = out.File
	file_device_control_proto_rawDesc = nil
	file_device_control_proto_goTypes = nil
	file_device_control_proto_depIdxs = nil
}
