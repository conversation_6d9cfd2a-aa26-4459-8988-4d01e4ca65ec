syntax = "proto3";

package main;
option go_package = "../internal/pb";

service DeviceControlService {
  rpc DeviceControl(DeviceControlRequest) returns (DeviceControlResponse) {}
}

message DeviceControlRequest {
  string action_type = 1;
  string dock_sn = 2;
  string drone_sn = 3;
}

message DeviceControlResponse {
  bool succeed = 1;
}


type DroneControl struct {
  X      float64 `json:"x"`
  Y      float64 `json:"y"`
  H      float64 `json:"h"`
  W      float64 `json:"w"`
}