syntax = "proto3";

package main;
option go_package = "../internal/pb";

service DeviceControlService {
  rpc DeviceControl(DeviceControlRequest) returns (DeviceControlResponse) {}
}

message DeviceControlRequest {
  string action_type = 1;
  string dock_sn = 2;
  string drone_sn = 3;
  DroneControl drone_control = 4;
  GimbalControl gimbal_control = 5;
}

message DeviceControlResponse {
  bool succeed = 1;
}

message DroneControl {
  double x = 1;
  double y = 2;
  double h = 3;
  double w = 4;
}

message GimbalControl {
  double pitch_adjust = 1;
  double yaw_adjust = 2;
  int32 zoom_type = 3;
  int32 gimbal_type = 4;
}