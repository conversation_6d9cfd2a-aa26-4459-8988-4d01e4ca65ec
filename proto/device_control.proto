syntax = "proto3";

package main;
option go_package = "../internal/pb";

// 操作类型枚举
enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;  // 未指定
  ACTION_TYPE_DRONE = 1;        // 无人机控制
  ACTION_TYPE_GIMBAL = 2;       // 云台控制
  ACTION_TYPE_PHOTO = 3;        // 拍照
}

// 变焦类型枚举
enum ZoomType {
  ZOOM_TYPE_UNSPECIFIED = 0;    // 未指定
  ZOOM_TYPE_IN = 1;             // 拉近焦距
  ZOOM_TYPE_OUT = 2;            // 拉远焦距
}

// 云台类型枚举
enum GimbalType {
  GIMBAL_TYPE_UNSPECIFIED = 0;  // 未指定
  GIMBAL_TYPE_ZOOM = 1;         // 变焦
  GIMBAL_TYPE_WIDE = 2;         // 广角
}

service DeviceControlService {
  rpc DeviceControl(DeviceControlRequest) returns (DeviceControlResponse) {}
}

message DeviceControlRequest {
  ActionType action_type = 1;
  string dock_sn = 2;
  string drone_sn = 3;
  DroneControl drone_control = 4;
  GimbalControl gimbal_control = 5;
}

message DeviceControlResponse {
  bool succeed = 1;
}

message DroneControl {
  double x = 1;
  double y = 2;
  double h = 3;
  double w = 4;
}

message GimbalControl {
  double pitch_adjust = 1;
  double yaw_adjust = 2;
  ZoomType zoom_type = 3;
  GimbalType gimbal_type = 4;
}