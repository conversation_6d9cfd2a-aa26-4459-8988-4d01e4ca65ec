# 设备控制 gRPC 服务文档

## 概述

设备控制 gRPC 服务提供了对无人机和云台设备的远程控制功能。该服务基于 Protocol Buffers 定义，支持多种设备控制操作。

## 服务定义

### 服务接口

```protobuf
service DeviceControlService {
  rpc DeviceControl(DeviceControlRequest) returns (DeviceControlResponse) {}
}
```

### 消息类型

#### DeviceControlRequest

```protobuf
message DeviceControlRequest {
  ActionType action_type = 1;       // 操作类型枚举
  string dock_sn = 2;               // 机场序列号
  string drone_sn = 3;              // 无人机序列号
  DroneControl drone_control = 4;   // 无人机控制参数（当action_type=ACTION_TYPE_DRONE时使用）
  GimbalControl gimbal_control = 5; // 云台控制参数（当action_type=ACTION_TYPE_GIMBAL时使用）
}
```

#### DeviceControlResponse

```protobuf
message DeviceControlResponse {
  bool succeed = 1;  // 操作是否成功
}
```

#### DroneControl

```protobuf
message DroneControl {
  double x = 1;  // X轴坐标/移动距离
  double y = 2;  // Y轴坐标/移动距离
  double h = 3;  // 高度/垂直移动距离
  double w = 4;  // 宽度/水平移动距离
}
```

#### GimbalControl

```protobuf
message GimbalControl {
  double pitch_adjust = 1;  // 俯仰角调整
  double yaw_adjust = 2;     // 偏航角调整
  ZoomType zoom_type = 3;    // 变焦类型枚举
  GimbalType gimbal_type = 4; // 云台类型枚举
}
```

### 枚举定义

#### ActionType

```protobuf
enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;  // 未指定
  ACTION_TYPE_DRONE = 1;        // 无人机控制
  ACTION_TYPE_GIMBAL = 2;       // 云台控制
  ACTION_TYPE_PHOTO = 3;        // 拍照
}
```

#### ZoomType

```protobuf
enum ZoomType {
  ZOOM_TYPE_UNSPECIFIED = 0;    // 未指定
  ZOOM_TYPE_IN = 1;             // 拉近焦距
  ZOOM_TYPE_OUT = 2;            // 拉远焦距
}
```

#### GimbalType

```protobuf
enum GimbalType {
  GIMBAL_TYPE_UNSPECIFIED = 0;  // 未指定
  GIMBAL_TYPE_ZOOM = 1;         // 变焦
  GIMBAL_TYPE_WIDE = 2;         // 广角
}
```

## 操作类型说明

### ACTION_TYPE_DRONE (1) - 无人机控制

支持以下无人机控制操作：
- 无人机朝机头方向推进指定距离
- 机头旋转（如右转90度）
- 位置移动控制

**示例请求：**
```json
{
  "action_type": "ACTION_TYPE_DRONE",
  "dock_sn": "DOCK001",
  "drone_sn": "DRONE001",
  "drone_control": {
    "x": 10.5,
    "y": 20.3,
    "h": 50.0,
    "w": 5.0
  }
}
```

### ACTION_TYPE_GIMBAL (2) - 云台控制

支持云台角度调整和变焦控制：
- 俯仰角调整
- 偏航角调整（如右转90度）
- 变焦控制（拉近/拉远）
- 云台类型选择（变焦/广角）

**示例请求：**
```json
{
  "action_type": "ACTION_TYPE_GIMBAL",
  "dock_sn": "DOCK001",
  "drone_sn": "DRONE001",
  "gimbal_control": {
    "pitch_adjust": 15.0,
    "yaw_adjust": 90.0,
    "zoom_type": "ZOOM_TYPE_IN",      // 拉近焦距
    "gimbal_type": "GIMBAL_TYPE_ZOOM" // 变焦云台
  }
}
```

### ACTION_TYPE_PHOTO (3) - 拍照控制

执行拍照操作，无需额外参数。

**示例请求：**
```json
{
  "action_type": "ACTION_TYPE_PHOTO",
  "dock_sn": "DOCK001",
  "drone_sn": "DRONE001"
}
```

## 服务配置

### 端口配置

gRPC 服务端口在配置文件中设置：

```yaml
grpc:
  port: "8080"
```

### 启动服务

使用以下命令启动 gRPC 服务：

```bash
./app grpc
```

或者直接运行：

```bash
go run main.go grpc
```

## 客户端示例

### Go 客户端

```go
package main

import (
    "context"
    "log"
    "time"
    
    "wukong-api/internal/pb"
    "google.golang.org/grpc"
)

func main() {
    // 连接到 gRPC 服务器
    conn, err := grpc.Dial("localhost:8080", grpc.WithInsecure())
    if err != nil {
        log.Fatalf("连接失败: %v", err)
    }
    defer conn.Close()

    // 创建客户端
    client := pb.NewDeviceControlServiceClient(conn)

    // 无人机控制示例
    req := &pb.DeviceControlRequest{
        ActionType: pb.ActionType_ACTION_TYPE_DRONE,
        DockSn:     "DOCK001",
        DroneSn:    "DRONE001",
        DroneControl: &pb.DroneControl{
            X: 10.0,
            Y: 0.0,
            H: 0.0,
            W: 0.0,
        },
    }

    ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
    defer cancel()

    resp, err := client.DeviceControl(ctx, req)
    if err != nil {
        log.Fatalf("调用失败: %v", err)
    }

    log.Printf("操作结果: %v", resp.Succeed)
}
```

## 开发说明

### 代码结构

- `proto/device_control.proto` - Protocol Buffers 定义文件
- `internal/pb/` - 生成的 protobuf Go 代码
- `internal/rpc/device_control.go` - gRPC 服务实现
- `internal/rpc/registry.go` - 服务注册器
- `cmd/grpc/server.go` - gRPC 服务器启动代码

### 重新生成 protobuf 代码

当修改 proto 文件后，使用以下命令重新生成代码：

```bash
make compile-grpc
```

### 测试

运行测试验证服务功能：

```bash
go test ./test -v
```

## 注意事项

1. 当前实现中，设备控制的具体逻辑需要根据实际硬件接口进行完善
2. 建议在生产环境中添加适当的错误处理和日志记录
3. 可以根据需要添加认证和授权机制
4. 建议添加请求参数验证逻辑
